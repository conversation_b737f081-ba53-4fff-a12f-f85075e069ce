/**
 * Lien Audio → Caméra
 * Automation qui contrôle la visibilité des caméras selon les niveaux audio des micros
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule } from '../interfaces/module.interface';
import { audioToCameraConfig } from '../../config';

// Cast de la configuration pour éviter les problèmes de type
const config = audioToCameraConfig as any;

interface AudioState {
    /** Niveau audio actuel (high/low) */
    level: 'high' | 'low';
    /** Timestamp de la dernière activation */
    lastActivated: number;
    /** Timestamp de la dernière désactivation */
    lastDeactivated: number;
}

interface CameraState {
    /** Source caméra actuellement visible */
    activeCamera: string | null;
    /** Timestamp de l'activation de la caméra active */
    activatedAt: number;
    /** Timer pour le délai d'activation */
    activationTimer: NodeJS.Timeout | null;
    /** Timer pour le délai de masquage */
    hideTimer: NodeJS.Timeout | null;
    /** Mode plein écran actif */
    isFullscreen: boolean;
    /** Timer pour le mode plein écran */
    fullscreenTimer: NodeJS.Timeout | null;
    /** Timestamp du début de la discussion multiple */
    multiMicStartTime: number;
}

export class AudioToCameraLink implements IModuleLink {
    public readonly name = 'audio-to-camera';
    public readonly description = 'Contrôle la visibilité des caméras selon les niveaux audio des micros';
    public readonly enabled: boolean;

    private obsModule?: IModule;
    private cleanupCallbacks: Array<() => void> = [];

    // États internes
    private audioStates = new Map<string, AudioState>();
    private cameraState: CameraState = {
        activeCamera: null,
        activatedAt: 0,
        activationTimer: null,
        hideTimer: null,
        isFullscreen: false,
        fullscreenTimer: null,
        multiMicStartTime: 0,
    };

    constructor(private linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && config?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            console.log('[AudioToCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[AudioToCameraLink] OBS module not found - link will not function');
            return;
        }

        // Initialiser les états audio pour chaque micro configuré
        config.micToCameraMapping.forEach((mapping: any) => {
            this.audioStates.set(mapping.micName, {
                level: 'low',
                lastActivated: 0,
                lastDeactivated: 0,
            });
        });

        // Écouter les événements audio du module OBS
        this.setupOBSListeners();

        console.log(`[AudioToCameraLink] Initialized with ${config.micToCameraMapping.length} mic-to-camera mappings`);
        if (config.debug.verbose) {
            console.log('[AudioToCameraLink] Configuration:', {
                targetScene: config.targetScene,
                audioThresholdDb: config.audioThresholdDb,
                activationDelayMs: config.activationDelayMs,
                holdDurationMs: config.holdDurationMs,
                mappings: config.micToCameraMapping,
            });
        }
    }

    async cleanup(): Promise<void> {
        // Nettoyer les timers
        if (this.cameraState.activationTimer) {
            clearTimeout(this.cameraState.activationTimer);
        }
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
        }
        if (this.cameraState.fullscreenTimer) {
            clearTimeout(this.cameraState.fullscreenTimer);
        }

        // Nettoyer les callbacks
        this.cleanupCallbacks.forEach((cleanup) => cleanup());
        this.cleanupCallbacks = [];

        console.log('[AudioToCameraLink] Cleaned up');
    }

    private setupOBSListeners(): void {
        if (!this.obsModule) return;

        // Écouter les événements de niveau audio
        const eventCallback = (event: any) => {
            if (event.type === 'input_volume_HIGH') {
                this.handleMicActivated(event.data.inputName);
            } else if (event.type === 'input_volume_LOW') {
                this.handleMicDeactivated(event.data.inputName);
            }
        };

        this.obsModule.onEvent(eventCallback);
        this.cleanupCallbacks.push(() => {
            // Note: Dans une implémentation complète, il faudrait pouvoir se désabonner
            // Pour l'instant, on se contente de vider le callback
        });
    }

    private handleMicActivated(micName: string): void {
        // Trouver la caméra associée à ce micro
        const mapping = config.micToCameraMapping.find((m: any) => m.micName === micName);
        if (!mapping) return;

        const cameraSource = mapping.cameraSource;
        const audioState = this.audioStates.get(micName);
        if (!audioState) return;

        // Mettre à jour l'état audio
        audioState.level = 'high';
        audioState.lastActivated = Date.now();
        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic activated: ${micName} → ${cameraSource}`);
        }

        // Annuler le timer de masquage s'il existe
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    private handleMicDeactivated(micName: string): void {
        // Mettre à jour l'état audio
        const audioState = this.audioStates.get(micName);
        if (audioState) {
            audioState.level = 'low';
            audioState.lastDeactivated = Date.now();
        }

        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic deactivated: ${micName}`);
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    /**
     * Évaluer l'état des caméras et décider de l'action à prendre
     */
    private evaluateCameraState(): void {
        // Grouper les micros actifs par caméra
        const activeCameraGroups = this.getActiveCameraGroups();

        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Active camera groups:`, Array.from(activeCameraGroups.entries()));
        }

        // Gérer le mode plein écran
        this.handleFullscreenMode(activeCameraGroups);

        // Si on est en mode plein écran, ne pas changer de caméra
        if (this.cameraState.isFullscreen) {
            return;
        }

        // Annuler le timer de masquage s'il y a des micros actifs
        if (activeCameraGroups.size > 0 && this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }

        if (activeCameraGroups.size === 0) {
            // Aucun micro actif - programmer le masquage si configuré
            if (config.fallbackBehavior.hideAllWhenInactive) {
                if (this.cameraState.hideTimer) {
                    clearTimeout(this.cameraState.hideTimer);
                }

                this.cameraState.hideTimer = setTimeout(() => {
                    void this.hideAllCameras();
                }, config.fallbackBehavior.hideDelayMs);
            }
        } else if (activeCameraGroups.size === 1) {
            // Une seule caméra a des micros actifs
            const [cameraSource] = activeCameraGroups.keys();

            // Vérifier la durée de maintien si une autre caméra est déjà active
            const now = Date.now();
            if (this.cameraState.activeCamera && this.cameraState.activeCamera !== cameraSource && now - this.cameraState.activatedAt < config.holdDurationMs) {
                if (config.debug.verbose) {
                    console.log(`[AudioToCameraLink] Camera ${this.cameraState.activeCamera} still in hold period, ignoring switch to ${cameraSource}`);
                }
                return;
            }

            // Activer la caméra avec délai ou immédiatement selon le contexte
            if (this.cameraState.activeCamera === null) {
                // Pas de caméra active, utiliser le délai d'activation
                if (this.cameraState.activationTimer) {
                    clearTimeout(this.cameraState.activationTimer);
                }

                this.cameraState.activationTimer = setTimeout(() => {
                    void this.activateCamera(cameraSource);
                }, config.activationDelayMs);
            } else {
                // Changement de caméra, activation immédiate
                void this.activateCamera(cameraSource);
            }
        }
        // Si activeCameraGroups.size > 1, la logique du mode plein écran s'en occupe
    }

    /**
     * Trouve le micro qui a été activé le plus récemment parmi une liste de micros actifs
     */
    private findMostRecentlyActivatedMic(activeMics: string[]): string | null {
        let mostRecentMic: string | null = null;
        let mostRecentTime = 0;

        for (const micName of activeMics) {
            const audioState = this.audioStates.get(micName);
            if (audioState && audioState.lastActivated > mostRecentTime) {
                mostRecentTime = audioState.lastActivated;
                mostRecentMic = micName;
            }
        }

        return mostRecentMic;
    }

    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            // Masquer toutes les caméras d'abord
            const allCameraSources = config.micToCameraMapping.map((m: any) => m.cameraSource);
            await (this.obsModule as any).hideAllSources(config.targetScene, allCameraSources);

            // Afficher la caméra sélectionnée
            await (this.obsModule as any).setSourceVisibility(config.targetScene, cameraSource, true);

            // Mettre à jour l'état
            this.cameraState.activeCamera = cameraSource;
            this.cameraState.activatedAt = Date.now();
            this.cameraState.activationTimer = null;

            console.log(`[AudioToCameraLink] Camera activated: ${cameraSource} in scene ${config.targetScene}`);
        } catch (error) {
            console.error(`[AudioToCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    private async hideAllCameras(): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = config.micToCameraMapping.map((m: any) => m.cameraSource);
            await (this.obsModule as any).hideAllSources(config.targetScene, allCameraSources);

            this.cameraState.activeCamera = null;
            this.cameraState.activatedAt = 0;
            this.cameraState.hideTimer = null;

            console.log(`[AudioToCameraLink] All cameras hidden in scene ${config.targetScene} (fullscreen mode)`);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to hide cameras:', error);
        }
    }

    /**
     * Grouper les micros actifs par caméra associée
     */
    private getActiveCameraGroups(): Map<string, string[]> {
        const cameraGroups = new Map<string, string[]>();

        // Parcourir tous les micros actifs
        for (const [micName, audioState] of this.audioStates.entries()) {
            if (audioState.level === 'high') {
                // Trouver la caméra associée à ce micro
                const mapping = config.micToCameraMapping.find((m: any) => m.micName === micName);
                if (mapping) {
                    const cameraSource = mapping.cameraSource;
                    if (!cameraGroups.has(cameraSource)) {
                        cameraGroups.set(cameraSource, []);
                    }
                    cameraGroups.get(cameraSource)!.push(micName);
                }
            }
        }

        return cameraGroups;
    }

    /**
     * Vérifier si on doit passer en mode plein écran
     */
    private shouldEnterFullscreen(activeCameraGroups: Map<string, string[]>): boolean {
        if (!config.fullscreenMode.enabled) {
            return false;
        }

        // Compter le nombre de caméras différentes qui ont des micros actifs
        const activeCameraCount = activeCameraGroups.size;

        return activeCameraCount >= config.fullscreenMode.minActiveMics;
    }

    /**
     * Gérer le mode plein écran
     */
    private handleFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        const shouldBeFullscreen = this.shouldEnterFullscreen(activeCameraGroups);
        const now = Date.now();

        if (shouldBeFullscreen && !this.cameraState.isFullscreen) {
            // Démarrer le timer pour le mode plein écran
            if (this.cameraState.multiMicStartTime === 0) {
                this.cameraState.multiMicStartTime = now;

                if (config.debug.verbose) {
                    console.log(`[AudioToCameraLink] Multiple cameras active, starting fullscreen timer`);
                }
            }

            // Vérifier si le délai est écoulé
            if (now - this.cameraState.multiMicStartTime >= config.fullscreenMode.multiMicDurationMs) {
                this.enterFullscreenMode();
            }
        } else if (!shouldBeFullscreen) {
            // Réinitialiser le timer si on n'a plus assez de caméras actives
            this.cameraState.multiMicStartTime = 0;

            if (this.cameraState.isFullscreen) {
                this.exitFullscreenMode(activeCameraGroups);
            }
        }
    }

    /**
     * Entrer en mode plein écran
     */
    private enterFullscreenMode(): void {
        if (this.cameraState.isFullscreen) return;

        this.cameraState.isFullscreen = true;

        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Entering fullscreen mode (multiple cameras active)`);
        }

        // Masquer toutes les caméras pour révéler CAM1
        void this.hideAllCameras();
    }

    /**
     * Sortir du mode plein écran
     */
    private exitFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        if (!this.cameraState.isFullscreen) return;

        this.cameraState.isFullscreen = false;
        this.cameraState.multiMicStartTime = 0;

        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Exiting fullscreen mode`);
        }

        // Activer la caméra appropriée selon les micros encore actifs
        if (activeCameraGroups.size === 1) {
            const [cameraSource] = activeCameraGroups.keys();
            void this.activateCamera(cameraSource);
        }
    }
}
