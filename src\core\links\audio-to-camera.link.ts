/**
 * Lien Audio → Caméra
 * Automation qui contrôle la visibilité des caméras selon les niveaux audio des micros
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule } from '../interfaces/module.interface';
import { audioToCameraConfig } from '../../config';

// Cast de la configuration pour éviter les problèmes de type
const config = audioToCameraConfig as any;

interface AudioState {
    /** Niveau audio actuel (high/low) */
    level: 'high' | 'low';
    /** Timestamp de la dernière activation */
    lastActivated: number;
    /** Timestamp de la dernière désactivation */
    lastDeactivated: number;
}

interface CameraState {
    /** Source caméra actuellement visible */
    activeCamera: string | null;
    /** Timestamp de l'activation de la caméra active */
    activatedAt: number;
    /** Timer pour le délai d'activation */
    activationTimer: NodeJS.Timeout | null;
    /** Timer pour le délai de masquage */
    hideTimer: NodeJS.Timeout | null;
    /** Mode plein écran actif */
    isFullscreen: boolean;
    /** Timer pour le mode plein écran */
    fullscreenTimer: NodeJS.Timeout | null;
    /** Timestamp du début de la discussion multiple */
    multiMicStartTime: number;
}

export class AudioToCameraLink implements IModuleLink {
    public readonly name = 'audio-to-camera';
    public readonly description = 'Contrôle la visibilité des caméras selon les niveaux audio des micros';
    public readonly enabled: boolean;

    private obsModule?: IModule;
    private cleanupCallbacks: Array<() => void> = [];

    // États internes
    private audioStates = new Map<string, AudioState>();
    private cameraState: CameraState = {
        activeCamera: null,
        activatedAt: 0,
        activationTimer: null,
        hideTimer: null,
    };

    constructor(private linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && config?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            console.log('[AudioToCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[AudioToCameraLink] OBS module not found - link will not function');
            return;
        }

        // Initialiser les états audio pour chaque micro configuré
        config.micToCameraMapping.forEach((mapping: any) => {
            this.audioStates.set(mapping.micName, {
                level: 'low',
                lastActivated: 0,
                lastDeactivated: 0,
            });
        });

        // Écouter les événements audio du module OBS
        this.setupOBSListeners();

        console.log(`[AudioToCameraLink] Initialized with ${config.micToCameraMapping.length} mic-to-camera mappings`);
        if (config.debug.verbose) {
            console.log('[AudioToCameraLink] Configuration:', {
                targetScene: config.targetScene,
                audioThresholdDb: config.audioThresholdDb,
                activationDelayMs: config.activationDelayMs,
                holdDurationMs: config.holdDurationMs,
                mappings: config.micToCameraMapping,
            });
        }
    }

    async cleanup(): Promise<void> {
        // Nettoyer les timers
        if (this.cameraState.activationTimer) {
            clearTimeout(this.cameraState.activationTimer);
        }
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
        }

        // Nettoyer les callbacks
        this.cleanupCallbacks.forEach((cleanup) => cleanup());
        this.cleanupCallbacks = [];

        console.log('[AudioToCameraLink] Cleaned up');
    }

    private setupOBSListeners(): void {
        if (!this.obsModule) return;

        // Écouter les événements de niveau audio
        const eventCallback = (event: any) => {
            if (event.type === 'input_volume_HIGH') {
                this.handleMicActivated(event.data.inputName);
            } else if (event.type === 'input_volume_LOW') {
                this.handleMicDeactivated(event.data.inputName);
            }
        };

        this.obsModule.onEvent(eventCallback);
        this.cleanupCallbacks.push(() => {
            // Note: Dans une implémentation complète, il faudrait pouvoir se désabonner
            // Pour l'instant, on se contente de vider le callback
        });
    }

    private handleMicActivated(micName: string): void {
        // Trouver la caméra associée à ce micro
        const mapping = config.micToCameraMapping.find((m: any) => m.micName === micName);
        if (!mapping) return;

        const cameraSource = mapping.cameraSource;
        const audioState = this.audioStates.get(micName);
        if (!audioState) return;

        // Mettre à jour l'état audio
        audioState.level = 'high';
        audioState.lastActivated = Date.now();
        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic activated: ${micName} → ${cameraSource}`);
        }

        // Annuler le timer de masquage s'il existe
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }

        // Si une caméra est déjà active, vérifier la durée de maintien
        const now = Date.now();
        if (this.cameraState.activeCamera && now - this.cameraState.activatedAt < config.holdDurationMs) {
            if (config.debug.verbose) {
                console.log(`[AudioToCameraLink] Camera ${this.cameraState.activeCamera} still in hold period, ignoring activation`);
            }
            return;
        }

        // Programmer l'activation avec délai
        if (this.cameraState.activationTimer) {
            clearTimeout(this.cameraState.activationTimer);
        }

        this.cameraState.activationTimer = setTimeout(() => {
            this.activateCamera(cameraSource);
        }, config.activationDelayMs);
    }

    private handleMicDeactivated(micName: string): void {
        // Mettre à jour l'état audio
        const audioState = this.audioStates.get(micName);
        if (audioState) {
            audioState.level = 'low';
            audioState.lastDeactivated = Date.now();
        }

        if (config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic deactivated: ${micName}`);
        }

        // Vérifier s'il reste des micros actifs
        const activeMics = Array.from(this.audioStates.entries())
            .filter(([, state]) => state.level === 'high')
            .map(([micName]) => micName);

        if (activeMics.length === 0) {
            // Aucun micro actif - programmer le masquage si configuré
            if (config.fallbackBehavior.hideAllWhenInactive) {
                if (this.cameraState.hideTimer) {
                    clearTimeout(this.cameraState.hideTimer);
                }

                this.cameraState.hideTimer = setTimeout(() => {
                    void this.hideAllCameras();
                }, config.fallbackBehavior.hideDelayMs);
            }
        } else {
            // Il reste des micros actifs - activer la caméra du micro le plus récemment activé
            const mostRecentMic = this.findMostRecentlyActivatedMic(activeMics);
            if (mostRecentMic) {
                const mapping = config.micToCameraMapping.find((m: any) => m.micName === mostRecentMic);
                if (mapping) {
                    if (config.debug.verbose) {
                        console.log(`[AudioToCameraLink] Switching back to camera for most recent active mic: ${mostRecentMic} → ${mapping.cameraSource}`);
                    }

                    // Annuler le timer de masquage s'il existe
                    if (this.cameraState.hideTimer) {
                        clearTimeout(this.cameraState.hideTimer);
                        this.cameraState.hideTimer = null;
                    }

                    // Activer immédiatement la caméra (pas de délai car c'est un retour)
                    void this.activateCamera(mapping.cameraSource);
                }
            }
        }
    }

    /**
     * Trouve le micro qui a été activé le plus récemment parmi une liste de micros actifs
     */
    private findMostRecentlyActivatedMic(activeMics: string[]): string | null {
        let mostRecentMic: string | null = null;
        let mostRecentTime = 0;

        for (const micName of activeMics) {
            const audioState = this.audioStates.get(micName);
            if (audioState && audioState.lastActivated > mostRecentTime) {
                mostRecentTime = audioState.lastActivated;
                mostRecentMic = micName;
            }
        }

        return mostRecentMic;
    }

    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            // Masquer toutes les caméras d'abord
            const allCameraSources = config.micToCameraMapping.map((m: any) => m.cameraSource);
            await (this.obsModule as any).hideAllSources(config.targetScene, allCameraSources);

            // Afficher la caméra sélectionnée
            await (this.obsModule as any).setSourceVisibility(config.targetScene, cameraSource, true);

            // Mettre à jour l'état
            this.cameraState.activeCamera = cameraSource;
            this.cameraState.activatedAt = Date.now();
            this.cameraState.activationTimer = null;

            console.log(`[AudioToCameraLink] Camera activated: ${cameraSource} in scene ${config.targetScene}`);
        } catch (error) {
            console.error(`[AudioToCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    private async hideAllCameras(): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = config.micToCameraMapping.map((m: any) => m.cameraSource);
            await (this.obsModule as any).hideAllSources(config.targetScene, allCameraSources);

            this.cameraState.activeCamera = null;
            this.cameraState.activatedAt = 0;
            this.cameraState.hideTimer = null;

            console.log(`[AudioToCameraLink] All cameras hidden in scene ${config.targetScene}`);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to hide cameras:', error);
        }
    }
}
