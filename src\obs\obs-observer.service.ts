/**
 * Service d'observation des événements OBS - Version ultra-simplifiée
 * Responsabilité : Écouter OBS, émettre des événements
 */

import { Injectable } from '@nestjs/common';
import OBSWebSocket, { EventSubscription } from 'obs-websocket-js';

export type OBSEventCallback = (type: string, data: any) => void;

interface AudioSourceConfig {
    inputName: string;
    channel: 'left' | 'right' | 'mono'; // mono = left par défaut
}

@Injectable()
export class OBSObserverService {
    private eventCallback?: OBSEventCallback;

    private readonly HIGH_DB = -40;
    private volumeState: Record<string, 'high' | 'low' | undefined> = {};
    private lastLogTime: Record<string, number> = {};
    private readonly LOG_INTERVAL_MS = 5000; // 5 secondes

    // Configuration des sources à écouter
    private readonly audioSources: AudioSourceConfig[] = [
        { inputName: 'mic_invite1', channel: 'left' },
        { inputName: 'mic_invite2', channel: 'left' },
        // Ajoutez d'autres sources ici selon vos besoins
        // { inputName: 'MIC2', channel: 'right' },
    ];

    constructor() {}

    setEventCallback(callback: OBSEventCallback): void {
        this.eventCallback = callback;
    }

    setupEventListeners(obs: OBSWebSocket): void {
        obs.on('InputMuteStateChanged', (data) => {
            this.handleInputMuteChanged(data.inputName, data.inputMuted);
        });

        obs.on('InputVolumeChanged', (data) => {
            this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        obs.on('CurrentProgramSceneChanged', (data) => {
            this.handleSceneChanged(data.sceneName);
        });

        obs.on('InputVolumeMeters', ({ inputs }) => {
            inputs.forEach((i) => {
                if (!i.inputName || !i.inputLevelsMul || (i.inputLevelsMul as number[]).length === 0) return;

                // Vérifier si cette source est configurée pour être écoutée
                const sourceConfig = this.audioSources.find((s) => s.inputName === i.inputName);
                if (!sourceConfig) return;

                const rawLinear = i.inputLevelsMul[0];
                const levels = Array.isArray(rawLinear) ? rawLinear : [rawLinear];

                // Sélectionner le canal selon la configuration
                let linear: number;
                switch (sourceConfig.channel) {
                    case 'right':
                        linear = (levels[1] as number) || 0;
                        break;
                    case 'left':
                    case 'mono':
                    default:
                        linear = (levels[0] as number) || 0;
                        break;
                }

                this.handleInputMeter(i.inputName as string, linear, sourceConfig.channel);
            });
        });
    }

    // === HANDLERS SIMPLES ===

    private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
        this.emitEvent('input_mute_changed', {
            inputName,
            inputMuted,
        });
    }

    private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
        this.emitEvent('input_volume_changed', {
            inputName,
            inputVolumeDb,
            inputVolumeMul,
        });
    }

    private handleSceneChanged(sceneName: string): void {
        this.emitEvent('scene_changed', {
            sceneName,
        });
    }

    private handleInputMeter(inputName: string, linearLvl: number, channel: string): void {
        const prev = this.volumeState[inputName];
        const now = Date.now();

        // Calculs de différentes unités audio
        const audioLevels = this.calculateAudioLevels(linearLvl);

        // Log périodique des niveaux (toutes les 5 secondes)
        const lastLog = this.lastLogTime[inputName] || 0;
        if (now - lastLog >= this.LOG_INTERVAL_MS) {
            console.log(`[obs-observer] ${inputName} (${channel}):`);
            console.log(`  Linear: ${linearLvl.toFixed(4)} (${(linearLvl * 100).toFixed(1)}%)`);
            console.log(`  dBFS: ${audioLevels.dBFS.toFixed(1)}dB`);
            console.log(`  dBu: ${audioLevels.dBu.toFixed(1)}dB`);
            console.log(`  dBV: ${audioLevels.dBV.toFixed(1)}dB`);
            console.log(`  VU: ${audioLevels.VU.toFixed(1)}VU`);
            console.log(`  Peak: ${audioLevels.peak.toFixed(1)}dB`);
            console.log(`  RMS: ${audioLevels.RMS.toFixed(1)}dB`);
            this.lastLogTime[inputName] = now;
        }

        // Détection des changements d'état (utilise dBFS pour le moment)
        if (audioLevels.dBFS > this.HIGH_DB && prev !== 'high') {
            this.volumeState[inputName] = 'high';
            this.emitEvent('input_volume_HIGH', { inputName, channel, dB: audioLevels.dBFS });
        } else if (audioLevels.dBFS <= this.HIGH_DB && prev !== 'low') {
            this.volumeState[inputName] = 'low';
            this.emitEvent('input_volume_LOW', { inputName, channel, dB: audioLevels.dBFS });
        }
    }

    private calculateAudioLevels(linearLvl: number) {
        // Protection contre les valeurs nulles/négatives
        const safeLinear = Math.max(linearLvl, 1e-8);

        return {
            // dBFS (decibels Full Scale) - Standard numérique
            dBFS: 20 * Math.log10(safeLinear),

            // dBu (decibels unloaded) - Standard professionnel
            // Référence: 0 dBu = 0.775V RMS
            dBu: 20 * Math.log10(safeLinear) + 2.2, // Approximation courante

            // dBV (decibels Volt) - Référence 1V
            dBV: 20 * Math.log10(safeLinear) - 2.2,

            // VU (Volume Unit) - Échelle VU-mètre classique
            // 0 VU ≈ -20 dBFS en numérique
            VU: 20 * Math.log10(safeLinear) + 20,

            // Peak level (crête) - Souvent utilisé dans les DAW
            peak: 20 * Math.log10(safeLinear) + 6,

            // RMS approximation (Root Mean Square)
            RMS: 20 * Math.log10(safeLinear) - 3,
        };
    }

    private emitEvent(type: string, data: any): void {
        if (this.eventCallback) {
            this.eventCallback(type, data);
        }
    }
}
