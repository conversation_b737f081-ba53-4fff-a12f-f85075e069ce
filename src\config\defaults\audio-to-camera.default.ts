/**
 * Configuration par défaut pour l'automation audio-to-camera
 *
 * ⚠️ NE PAS MODIFIER CE FICHIER
 * Copiez cette configuration dans src/config/local/audio-to-camera.local.ts
 * pour personnaliser les paramètres selon vos besoins.
 */

export interface MicToCameraMapping {
    /** Nom du micro dans OBS (doit correspondre exactement) */
    micName: string;
    /** Nom de la source caméra dans OBS (doit correspondre exactement) */
    cameraSource: string;
}

export interface AudioToCameraConfig {
    /** Activer/désactiver l'automation */
    enabled: boolean;

    /** Nom de la scène OBS qui contient les caméras à contrôler */
    targetScene: string;

    /** Seuil audio en dB pour déclencher l'activation d'une caméra */
    audioThresholdDb: number;

    /** Délai minimum avant activation d'une caméra (en ms) */
    activationDelayMs: number;

    /** Durée minimum de maintien d'une caméra active (en ms) */
    holdDurationMs: number;

    /** D<PERSON>lai minimum entre tous les changements de caméra (en ms) */
    minChangeIntervalMs: number;

    /** Associations micros → caméras */
    micToCameraMapping: MicToCameraMapping[];

    /** Comportement quand aucun micro n'est actif */
    fallbackBehavior: {
        /** Masquer toutes les caméras quand aucun micro n'est actif */
        hideAllWhenInactive: boolean;
        /** Délai avant de masquer toutes les caméras (en ms) */
        hideDelayMs: number;
    };

    /** Mode plein écran pour discussions multiples */
    fullscreenMode: {
        /** Activer le mode plein écran quand plusieurs micros parlent */
        enabled: boolean;
        /** Durée minimum de discussion multiple avant passage en plein écran (en ms) */
        multiMicDurationMs: number;
        /** Nombre minimum de micros actifs pour déclencher le plein écran */
        minActiveMics: number;
    };

    /** Options de debug */
    debug: {
        /** Afficher les logs détaillés */
        verbose: boolean;
        /** Afficher les niveaux audio périodiquement */
        logAudioLevels: boolean;
    };
}

export const audioToCameraDefaultConfig: AudioToCameraConfig = {
    enabled: false, // Désactivé par défaut - à activer dans le fichier local
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,
    holdDurationMs: 4000,
    minChangeIntervalMs: 1000, // 1 seconde minimum entre changements
    micToCameraMapping: [
        // Exemples de configuration - à personnaliser dans le fichier local
        { micName: 'mic_invite1', cameraSource: 'CAM 1' },
        { micName: 'mic_invite2', cameraSource: 'CAM 2' },
        { micName: 'mic_invite3', cameraSource: 'CAM 3' },
    ],
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 2000,
    },
    fullscreenMode: {
        enabled: true,
        multiMicDurationMs: 3000, // 3 secondes de discussion multiple
        minActiveMics: 2, // Au moins 2 micros actifs
    },
    debug: {
        verbose: false,
        logAudioLevels: false,
    },
};
